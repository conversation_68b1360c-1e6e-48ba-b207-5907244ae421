/**
 * PayVendy Admin API Routes
 *
 * Admin endpoints for basic administrative functions.
 */

const express = require('express');
const { body, param, validationResult } = require('express-validator');
const userService = require('../services/userService');
const authService = require('../services/authService');
const logger = require('../utils/logger');
const crashReportsDashboard = require('./admin/crashReportsDashboard');

const router = express.Router();

// Mount crash reports dashboard routes
router.use('/crash-reports', crashReportsDashboard);

// Basic admin routes can be added here as needed

module.exports = router;


