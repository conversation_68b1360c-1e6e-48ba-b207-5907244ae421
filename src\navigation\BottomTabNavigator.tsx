import React, { memo, useMemo, useCallback } from 'react';
import { createBottomTabNavigator, BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { View, StyleSheet } from 'react-native';
import { useTheme } from '../components/ThemeContext';
import { BottomTabParamList, RootStackParamList } from '../types/navigation';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { 
  HomeIcon, 
  WalletIcon, 
  ProfileIcon,
  RewardsIcon,
  PaymentCardIcon
} from '../components/icons';

import HomeScreen from '../screens/CustomHome/CustomHomeScreen';
import HistoryScreen from '../screens/History/HistoryScreen';
import ProfileScreen from '../screens/Profile/ProfileScreen';
import ReferEarnScreen from '../screens/Refer/ReferEarnScreen';
import PaymentsScreen from '../screens/Payments/PaymentsScreen';

const Tab = createBottomTabNavigator<BottomTabParamList>();

// Type-safe wrapper component that properly adapts tab navigation to stack navigation
type HomeTabScreenProps = BottomTabScreenProps<BottomTabParamList, 'HomeTab'>;

const HomeTabScreen: React.FC<HomeTabScreenProps> = ({ navigation }) => {
  // Create a type-safe navigation object that matches what HomeScreen expects
  const stackNavigation = {
    ...navigation,
    navigate: (screen: keyof RootStackParamList, params?: any) => {
      // Handle navigation to stack screens from tab context
      if (screen === 'Home') {
        // Already on home tab, no need to navigate
        return;
      }
      // Navigate to other stack screens
      navigation.navigate(screen as any, params);
    },
    push: (screen: keyof RootStackParamList, params?: any) => {
      navigation.navigate(screen as any, params);
    },
    replace: (screen: keyof RootStackParamList, params?: any) => {
      navigation.navigate(screen as any, params);
    },
    goBack: () => navigation.goBack(),
    canGoBack: () => navigation.canGoBack(),
  };

  const stackRoute = {
    key: 'Home',
    name: 'Home' as const,
    params: undefined,
  };

  return <HomeScreen navigation={stackNavigation as any} route={stackRoute as any} />;
};

interface TabIconProps {
  focused: boolean;
  color: string;
  size: number;
  icon: React.ComponentType<{ size?: number; color?: string }>;
}

const TabIcon: React.FC<TabIconProps> = memo(({ focused, color, icon: IconComponent }) => {
  const { isDark } = useTheme();

  // Memoize the icon color calculation
  const iconColor = useMemo(() =>
    focused ? (isDark ? '#fff' : '#000') : color,
    [focused, isDark, color]
  );

  // Memoize the icon background style
  const iconBackgroundStyle = useMemo(() => [
    styles.iconBackground,
    { marginTop: 8 }
  ], []);

  return (
    <View style={styles.tabIconContainer}>
      <View style={iconBackgroundStyle}>
        <IconComponent
          size={26}
          color={iconColor}
        />
      </View>
    </View>
  );
});

const BottomTabNavigator: React.FC = memo(() => {
  const { theme } = useTheme();

  // Memoize screen options for performance
  const screenOptions = useMemo(() => ({
    headerShown: false,
    tabBarStyle: {
      backgroundColor: theme.colors.background,
      borderTopWidth: 0,
      height: 70,
      paddingBottom: 8,
      paddingTop: 8,
      elevation: 0,
    },
    tabBarActiveTintColor: theme.colors.primary,
    tabBarInactiveTintColor: theme.colors.muted,
    tabBarShowLabel: false,
  }), [theme]);

  // Memoized tab icon render functions for performance
  const renderHomeIcon = useCallback(({ focused, color, size }: any) => (
    <TabIcon focused={focused} color={color} size={size} icon={HomeIcon} />
  ), []);

  const renderHistoryIcon = useCallback(({ focused, color, size }: any) => (
    <TabIcon focused={focused} color={color} size={size} icon={WalletIcon} />
  ), []);

  const renderRewardsIcon = useCallback(({ focused, color, size }: any) => (
    <TabIcon focused={focused} color={color} size={size} icon={RewardsIcon} />
  ), []);

  const renderPaymentsIcon = useCallback(({ focused, color, size }: any) => (
    <TabIcon focused={focused} color={color} size={size} icon={PaymentCardIcon} />
  ), []);

  const renderProfileIcon = useCallback(({ focused, color, size }: any) => (
    <TabIcon focused={focused} color={color} size={size} icon={ProfileIcon} />
  ), []);

  return (
    <Tab.Navigator screenOptions={screenOptions}>
      <Tab.Screen
        name="HomeTab"
        component={HomeTabScreen}
        options={{ tabBarIcon: renderHomeIcon }}
      />
      <Tab.Screen
        name="HistoryTab"
        component={HistoryScreen}
        options={{ tabBarIcon: renderHistoryIcon }}
      />
      <Tab.Screen
        name="RewardsTab"
        component={ReferEarnScreen}
        options={{ tabBarIcon: renderRewardsIcon }}
      />
      <Tab.Screen
        name="PaymentsTab"
        component={PaymentsScreen}
        options={{ tabBarIcon: renderPaymentsIcon }}
      />
      <Tab.Screen
        name="ProfileTab"
        component={ProfileScreen}
        options={{ tabBarIcon: renderProfileIcon }}
      />
    </Tab.Navigator>
  );
});

const styles = StyleSheet.create({
  tabIconContainer: {
    alignItems: 'center',
    justifyContent: 'flex-end', // Move icon to bottom
    flex: 1,
  },
  iconBackground: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIcon: {
    // Icon styles handled in component
  },
  tabLabel: {
    fontSize: 10,
    fontWeight: '500',
  },
});

export default BottomTabNavigator;
