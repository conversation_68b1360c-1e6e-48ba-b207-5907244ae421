import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Image,
  ScrollView,
  RefreshControl,
  ImageBackground,
  Animated,
  Platform,
} from 'react-native';
import type { NativeStackScreenProps } from '@react-navigation/native-stack';
import type { RootStackParamList } from '../../types/navigation';
import { useTheme } from '../../components/ThemeContext';
import { userService, UserProfile } from '../../services/userService';
import {
  ClockSvgIcon,
  SunIcon,
  MoonIcon,
  PlusIcon,
  EyeIcon,
  EyeOffIcon,
  ArrowDownIcon,
} from '../../components/icons';
import { SafeAreaView as RNSafeAreaView } from 'react-native-safe-area-context';
import Clipboard from '@react-native-clipboard/clipboard';
import ClipboardPhoneModal from '../../components/ClipboardPhoneModal';
import MoreProductsModal from '../../components/MoreProductsModal';
import LocalAvatarImage from '../../components/LocalAvatarImage';
import GlassyBox from '../../components/GlassyBox';
import { BlurView } from '@react-native-community/blur';
import logger from '../../services/productionLogger';

// Memoized ServiceItem component to prevent unnecessary re-renders
const ServiceItem = React.memo(({ 
  iconSource, 
  label, 
  onPress 
}: {
  iconSource: any;
  label: string;
  onPress: () => void;
}) => {
  const { theme } = useTheme();
  
  const styles = useMemo(() => StyleSheet.create({
    serviceItem: {
      width: '30%',
      alignItems: 'center',
      marginBottom: 16,
    },
    serviceIcon: {
      width: 44,
      height: 44,
      borderRadius: 22,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 6,
    },
    serviceLabel: {
      color: theme.colors.text,
      fontSize: 11,
      fontWeight: '500',
      textAlign: 'center',
    },
  }), [theme]);
  
  return (
    <TouchableOpacity 
      style={styles.serviceItem}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.serviceIcon, { backgroundColor: 'transparent' }]}> 
        <Image source={iconSource} style={{ width: 28, height: 28, resizeMode: 'contain' }} />
      </View>
      <Text style={styles.serviceLabel}>{label}</Text>
    </TouchableOpacity>
  );
});

type Props = NativeStackScreenProps<RootStackParamList, 'Home'>;

const CustomHomeScreen = ({ navigation }: Props) => {
  const { theme, isDark } = useTheme();
  const [currentTime, setCurrentTime] = useState(new Date());
  // greeting is now computed via useMemo
  const [userData, setUserData] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [balanceVisible, setBalanceVisible] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [clipboardPhone, setClipboardPhone] = useState<string | null>(null);
  const [showClipboardModal, setShowClipboardModal] = useState(false);
  const [clipboardChecked, setClipboardChecked] = useState(false);
  const [showMoreProductsModal, setShowMoreProductsModal] = useState(false);

  // Animation values for enhanced UI
  const fadeAnim = useMemo(() => new Animated.Value(0), []);
  const slideUpAnim = useMemo(() => new Animated.Value(50), []);
  const scaleAnim = useMemo(() => new Animated.Value(0.95), []);

  // Load user data from new API endpoint
  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      logger.info('Loading user data from new API', null, 'home');
      setError(null);
      
      // Use the new user service to get profile data
      const profileResponse = await userService.getUserProfile();
      logger.info('Profile response received', { hasUser: !!profileResponse?.user }, 'home');
      
      if (profileResponse && profileResponse.user) {
        const user = profileResponse.user;
        logger.info('User data loaded successfully', { userId: user.id }, 'home');
        setUserData(user);
      } else {
        throw new Error('No user data received from API');
      }
      
    } catch (error: any) {
      logger.error('Error loading user data', error, 'home');
      setError(error.message || 'Failed to load user data');
      
      // Fallback to default data so the UI doesn't break
      setUserData({
        id: 'unknown',
        firstName: 'User',
        lastName: '',
        email: '',
        isEmailVerified: false,
        isPhoneVerified: false,
        balance: 1250.50, // Mock balance for demo
      });
    } finally {
      setLoading(false);
    }
  };

  // Initialize animations on mount
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideUpAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, slideUpAnim, scaleAnim]);

  // Refresh user data - memoized to prevent recreation
  const refreshUserData = useCallback(async () => {
    try {
      logger.info('Refreshing user data', null, 'home');
      const profileResponse = await userService.refreshUserProfile();
      if (profileResponse && profileResponse.user) {
        setUserData(profileResponse.user);
        setError(null);
      }
    } catch (error: any) {
      logger.error('Error refreshing user data', error, 'home');
      setError(error.message || 'Failed to refresh user data');
    }
  }, []);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  // Set greeting based on time - memoized to avoid recalculation
  const greeting = useMemo(() => {
    const hour = currentTime.getHours();
    if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 17) {
      return 'Good Afternoon';
    } else {
      return 'Good Evening';
    }
  }, [currentTime]);

  // Memoize date formatting to avoid repeated calculations
  const formattedDateTime = useMemo(() => {
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'short',
      day: 'numeric',
      month: 'long',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    };
    return currentTime.toLocaleDateString('en-US', options);
  }, [currentTime]);

  // Get the appropriate time icon based on current time - memoized
  const timeIcon = useMemo(() => {
    const hour = currentTime.getHours();
    
    if (hour >= 6 && hour < 18) {
      // Daytime (6 AM to 6 PM) - Show Sun
      return <SunIcon size={16} color="#FFA500" />;
    } else {
      // Nighttime (6 PM to 6 AM) - Show Moon
      return <MoonIcon size={16} color="#4A90E2" />;
    }
  }, [currentTime]);

  // Format balance for display - memoized to avoid repeated formatting
  const formattedBalance = useMemo(() => {
    if (!userData?.balance) return '₦0.00';
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 2,
    }).format(userData.balance);
  }, [userData?.balance]);

  // Mock commission data - replace with real data from API
  const commissionEarned = 89.50;

  // Memoized event handlers to prevent recreation on every render
  const handleAddBalance = useCallback(() => {
    // TODO: Navigate to add balance screen or show modal
    logger.userAction('ADD_BALANCE_PRESSED', null);
  }, []);

  const handleMoreOptions = useCallback(() => {
    // TODO: Show more balance options (withdraw, transfer, etc.)
    logger.userAction('MORE_OPTIONS_PRESSED', null);
  }, []);

  const handleServicePress = useCallback((service: string) => {
    logger.userAction('SERVICE_PRESSED', { service });
    // Navigate to respective service screens
    switch (service) {
      case 'buy-data':
        // navigation.navigate('BuyData');
        break;
      case 'buy-airtime':
        navigation.navigate('Airtime' as never);
        break;
      case 'pay-bills':
        // navigation.navigate('PayBills');
        break;
      case 'cable-tv':
        // navigation.navigate('CableTV');
        break;
      case 'transfer':
        // navigation.navigate('Transfer');
        break;
      case 'see-more':
        setShowMoreProductsModal(true);
        break;
      default:
        logger.warn('Unknown service selected', { service }, 'home');
    }
  }, [navigation]);

  // Clipboard phone detection on mount (only once per session)
  useEffect(() => {
    if (clipboardChecked) return;
    const checkClipboardForPhone = async () => {
      try {
        const content = await Clipboard.getString();

        // Debug logging to help identify false positives
        if (__DEV__ && content) {
          console.log('🔍 [CLIPBOARD_DEBUG] Checking clipboard content:', content.substring(0, 100));
        }

        // More precise Nigerian phone patterns to avoid false positives
        // Pattern 1: +234 followed by 10 digits (international format)
        const intlMatch = content.match(/\+234[789]\d{9}(?!\d)/);
        // Pattern 2: 234 followed by 10 digits (without +)
        const countryMatch = content.match(/(?<!\d)234[789]\d{9}(?!\d)/);
        // Pattern 3: 0 followed by 10 digits starting with 7, 8, or 9 (local format)
        const localMatch = content.match(/(?<!\d)0[789]\d{9}(?!\d)/);

        const phoneMatch = intlMatch || countryMatch || localMatch;
        if (phoneMatch) {
          if (__DEV__) {
            console.log('📱 [CLIPBOARD_DEBUG] Phone detected:', phoneMatch[0]);
            console.log('📱 [CLIPBOARD_DEBUG] Match type:', intlMatch ? 'international' : countryMatch ? 'country' : 'local');
          }
          setClipboardPhone(phoneMatch[0]);
          setShowClipboardModal(true);
        } else if (__DEV__ && content) {
          console.log('❌ [CLIPBOARD_DEBUG] No phone number detected in clipboard');
        }
      } catch (e) {
        // Ignore errors
      } finally {
        setClipboardChecked(true);
      }
    };
    checkClipboardForPhone();
  }, [clipboardChecked]);

  const handleIgnoreClipboard = () => {
    setShowClipboardModal(false);
    setClipboardPhone(null);
  };

  const handleUseClipboard = (phone: string) => {
    setShowClipboardModal(false);
    setClipboardPhone(null);
    // Optionally: store in context or navigate to Airtime/Data screen with phone
    navigation.navigate('Airtime'); // Remove params for now, as Airtime expects no params
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    safeArea: {
      flex: 1,
    },
    animatedContainer: {
      flex: 1,
      paddingHorizontal: 16,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: 32,
      paddingTop: 16,
    },
    headerCard: {
      marginBottom: 24,
      paddingVertical: 16,
      paddingHorizontal: 20,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    leftSection: {
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      flex: 1,
    },
    rightSection: {
      justifyContent: 'flex-end',
      alignItems: 'flex-end',
      flex: 1,
    },
    profileButton: {
      padding: 8,
    },
    profileWrapper: {
      position: 'relative',
    },
    profileStatusDot: {
      position: 'absolute',
      bottom: 2,
      right: 2,
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: '#34C759',
      borderWidth: 2,
      borderColor: '#FFFFFF',
    },
    clockButton: {
      padding: 8,
    },
    clockWrapper: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
    },
    profileImage: {
      width: 40,
      height: 40,
      borderRadius: 20,
      borderWidth: 2,
      borderColor: theme.colors.primary,
    },
    profilePlaceholder: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: theme.colors.primary,
    },
    profileInitials: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },
    // clockButton: {
    //   padding: 8,
    //   marginTop: -8, // move up
    // },
    greetingSection: {
      alignItems: 'center',
      paddingHorizontal: 16,
      marginBottom: 20,
    },
    dateTimeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
    },
    timeIconWrapper: {
      marginRight: 8,
      padding: 4,
    },
    dateTime: {
      color: theme.colors.text,
      fontSize: 14,
      fontWeight: '600',
      opacity: 0.8,
    },
    greetingTextContainer: {
      alignItems: 'center',
      width: '100%',
    },
    greetingText: {
      color: theme.colors.text,
      fontSize: 20,
      fontWeight: '600',
      marginBottom: 12,
      fontFamily: 'System',
      textAlign: 'center',
    },
    userNameContainer: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      minWidth: 120,
      alignItems: 'center',
    },
    userName: {
      color: theme.colors.text,
      fontSize: 18,
      fontWeight: '600',
      fontFamily: 'System',
      letterSpacing: 0.3,
      textAlign: 'center',
    },
    balanceCard: {
      marginBottom: 24,
      padding: 20,
      borderRadius: 24,
      minHeight: 120,
    },
    balanceLabelUnderline: {
      width: 40,
      height: 2,
      backgroundColor: theme.colors.primary,
      marginTop: 4,
      borderRadius: 1,
    },
    balanceHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    balanceHeaderLeft: {
      flex: 1,
    },
    balanceLabel: {
      color: theme.colors.muted,
      fontSize: 13,
      fontWeight: '500',
    },
    balanceAmountContainer: {
      marginTop: 16,
      marginBottom: 16,
    },
    balanceDisplayWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    eyeButton: {
      padding: 8,
      marginLeft: 16,
    },
    eyeIconWrapper: {
      padding: 6,
      borderRadius: 16,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.06)',
    },
    addBalanceIconWrapper: {
      marginRight: 8,
      padding: 2,
    },
    balanceActions: {
      flexDirection: 'row',
      gap: 8,
    },
    addBalanceButton: {
      backgroundColor: isDark ? '#FFFFFF' : '#000000',
      borderRadius: 20,
      paddingVertical: 8,
      paddingHorizontal: 12,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 4,
      shadowColor: isDark ? '#FFFFFF' : '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 3,
    },
    addBalanceText: {
      color: isDark ? '#000000' : '#FFFFFF',
      fontSize: 12,
      fontWeight: '600',
    },
    moreButton: {
      width: 32,
      height: 32,
      borderRadius: 20,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    balanceAmount: {
      color: theme.colors.text,
      fontSize: 20,
      fontWeight: '600',
      letterSpacing: -0.2,
      flex: 1,
    },
    commissionSection: {
      marginTop: 16,
      paddingTop: 16,
      borderTopWidth: 1,
      borderTopColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)',
    },
    commissionWrapper: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    commissionLabel: {
      color: theme.colors.muted,
      fontSize: 14,
      fontWeight: '500',
    },
    commissionAmount: {
      color: theme.colors.primary,
      fontSize: 16,
      fontWeight: '700',
    },
    commissionContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 16,
      paddingTop: 16,
      borderTopWidth: 1,
      borderTopColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)',
    },
    commissionIndicator: {
      width: 4,
      height: 20,
      backgroundColor: theme.colors.primary,
      borderRadius: 2,
      marginTop: 8,
      alignSelf: 'flex-end',
    },
    servicesCard: {
      marginBottom: 24,
      padding: 20,
      borderRadius: 20,
    },
    servicesTitle: {
      color: theme.colors.text,
      fontSize: 18,
      fontWeight: '600',
      marginBottom: 16,
      textAlign: 'center',
    },
    servicesGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      width: '100%',
      alignSelf: 'center',
    },
    serviceItem: {
      width: '30%',
      alignItems: 'center',
      marginBottom: 16,
    },
    serviceIcon: {
      width: 44,
      height: 44,
      borderRadius: 22,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 6,
    },
    serviceIconText: {
      fontSize: 18,
    },
    serviceLabel: {
      color: theme.colors.text,
      fontSize: 12,
      fontWeight: '500',
      textAlign: 'center',
    },
    seeMoreButton: {
      backgroundColor: isDark ? theme.colors.card : '#000',
      borderRadius: 12,
      paddingVertical: 10,
      paddingHorizontal: 16,
      alignItems: 'center',
      marginTop: 12,
      shadowColor: theme.colors.primary,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 3,
      flexDirection: 'row',
      justifyContent: 'center',
    },
    seeMoreText: {
      color: isDark ? theme.colors.text : '#fff',
      fontSize: 14,
      fontWeight: '600',
      marginRight: 8,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    placeholder: {
      color: theme.colors.muted,
      fontSize: 16,
      textAlign: 'center',
      marginBottom: 10,
    },
    loadingText: {
      color: theme.colors.muted,
      fontSize: 14,
    },
    errorText: {
      color: '#FF3B30',
      fontSize: 14,
      textAlign: 'center',
      marginBottom: 10,
    },
    retryButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 20,
      paddingVertical: 10,
      borderRadius: 8,
      marginTop: 10,
    },
    retryButtonText: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: '600',
    },
    debugText: {
      fontSize: 12,
      color: theme.colors.muted,
      textAlign: 'center',
      marginTop: 20,
      paddingHorizontal: 20,
    },
    textSkeleton: {
      width: 120,
      height: 18,
      borderRadius: 6,
      backgroundColor: theme.colors.border,
      marginBottom: 8,
      alignSelf: 'center',
    },
    textSkeletonSmall: {
      width: 60,
      height: 16,
      borderRadius: 6,
      backgroundColor: theme.colors.border,
      marginBottom: 8,
      alignSelf: 'center',
    },
  });

  const renderProfilePicture = () => {
    if (!userData) {
      return (
        <View style={styles.profilePlaceholder}>
          <Text style={styles.profileInitials}>?</Text>
        </View>
      );
    }

    // Check for profile picture/avatar URL
    const profileImageUrl = userData.picture || userData.avatar;
    
    if (profileImageUrl) {
      return (
        <LocalAvatarImage
          avatarPath={profileImageUrl}
          style={styles.profileImage}
          resizeMode="cover"
        />
      );
    } else {
      // Show initials if no profile picture
      const firstName = userData.firstName || '';
      const lastName = userData.lastName || '';
      const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
      
      return (
        <View style={styles.profilePlaceholder}>
          <Text style={styles.profileInitials}>{initials || 'U'}</Text>
        </View>
      );
    }
  };

  const getUserDisplayName = () => {
    if (!userData) return 'User!';
    
    const firstName = userData.firstName || '';
    
    if (firstName) {
      return `${firstName}!`;
    } else if (userData.email) {
      return `${userData.email.split('@')[0]}!`;
    } else {
      return 'User!';
    }
  };

  // Add pull-to-refresh functionality
  const onRefresh = async () => {
    setRefreshing(true);
    await loadUserData();
    setRefreshing(false);
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent={true}
      />

      {/* Background Image */}
      <ImageBackground
        source={isDark ? require('../../../assets/images/bg.jpeg') : require('../../../assets/images/bg-white.jpeg')}
        style={StyleSheet.absoluteFillObject}
        resizeMode="cover"
      />

      <RNSafeAreaView style={styles.safeArea} edges={["top", "left", "right"]}>
        <Animated.View
          style={[
            styles.animatedContainer,
            {
              opacity: fadeAnim,
              transform: [
                { translateY: slideUpAnim },
                { scale: scaleAnim }
              ]
            }
          ]}
        >
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor={theme.colors.primary} />
            }
            showsVerticalScrollIndicator={false}
          >
            {/* Enhanced Header with Glassmorphism */}
            <GlassyBox style={styles.headerCard} intensity="light">
              <View style={styles.header}>
                <View style={styles.leftSection}>
                  <TouchableOpacity
                    style={styles.profileButton}
                    activeOpacity={0.8}
                    // onPress={() => navigation.navigate('SetupTest')}
                  >
                    <View style={styles.profileWrapper}>
                      {renderProfilePicture()}
                      <View style={styles.profileStatusDot} />
                    </View>
                  </TouchableOpacity>
                </View>

                <View style={styles.rightSection}>
                  <TouchableOpacity
                    style={styles.clockButton}
                    onPress={() => navigation.navigate('HistoryTab' as never)}
                    activeOpacity={0.7}
                  >
                    <View style={styles.clockWrapper}>
                      <ClockSvgIcon size={22} color={theme.colors.text} />
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </GlassyBox>

            {/* Enhanced Greeting Section */}
            <View style={styles.greetingSection}>
              {/* Date/Time with Enhanced Icon */}
              <View style={styles.dateTimeContainer}>
                <View style={styles.timeIconWrapper}>
                  {timeIcon}
                </View>
                <Text style={styles.dateTime}>
                  {formattedDateTime}
                </Text>
              </View>

              <Animated.View style={[styles.greetingTextContainer, { opacity: fadeAnim }]}>
                <Text style={styles.greetingText}>
                  {loading ? <View style={styles.textSkeleton} /> : greeting}
                </Text>
                <GlassyBox style={styles.userNameContainer} intensity="light">
                  <Text style={styles.userName}>
                    {getUserDisplayName()}
                  </Text>
                </GlassyBox>
              </Animated.View>
            </View>

            {/* Enhanced Balance Card */}
            <GlassyBox style={styles.balanceCard} intensity="medium" glow={true}>
              <View style={styles.balanceHeader}>
                <View style={styles.balanceHeaderLeft}>
                  <Text style={styles.balanceLabel}>Available Balance</Text>
                </View>
                
                {/* Balance Actions at Top */}
                <View style={styles.balanceActions}>
                  <TouchableOpacity 
                    style={styles.addBalanceButton}
                    onPress={handleAddBalance}
                    activeOpacity={0.8}
                  >
                    <PlusIcon size={12} color={isDark ? '#000000' : '#FFFFFF'} />
                    <Text style={styles.addBalanceText}>Add Money</Text>
                  </TouchableOpacity>
                  {/* Removed the moreButton (three dots) */}
                </View>
              </View>

              {/* Balance Amount with Eye Icon */}
              <View style={styles.balanceAmountContainer}>
                <Text style={styles.balanceAmount}>
                  {loading ? <View style={styles.textSkeletonSmall} /> : (balanceVisible 
                    ? formattedBalance 
                    : '••••••')}
                </Text>
                <TouchableOpacity 
                  style={styles.eyeButton}
                  onPress={() => setBalanceVisible(!balanceVisible)}
                >
                  {balanceVisible ? 
                    <EyeIcon size={16} color={theme.colors.muted} /> :
                    <EyeOffIcon size={16} color={theme.colors.muted} />
                  }
                </TouchableOpacity>
              </View>

              {/* Commission Earned */}
              <View style={styles.commissionContainer}>
                <Text style={styles.commissionLabel}>Commission Earned:</Text>
                <Text style={styles.commissionAmount}>
                  {balanceVisible ? new Intl.NumberFormat('en-NG', {
                    style: 'currency',
                    currency: 'NGN',
                    minimumFractionDigits: 2,
                  }).format(commissionEarned) : '₦••••'}
                </Text>
              </View>
            </GlassyBox>

            {/* Enhanced Services Card */}
            <GlassyBox style={styles.servicesCard} intensity="light">
              <Text style={styles.servicesTitle}>Quick Services</Text>
              <View style={styles.servicesGrid}>
                <ServiceItem
                  iconSource={require('../../../assets/icons/mobile-data.png')}
                  label="Buy Data"
                  onPress={() => handleServicePress('buy-data')}
                />
                <ServiceItem
                  iconSource={require('../../../assets/icons/charging-battery.png')}
                  label="Buy Airtime"
                  onPress={() => handleServicePress('buy-airtime')}
                />
                <ServiceItem
                  iconSource={require('../../../assets/icons/pay.png')}
                  label="Pay Bills"
                  onPress={() => handleServicePress('pay-bills')}
                />
              </View>
              
              {/* See More Button */}
              <TouchableOpacity 
                style={[styles.seeMoreButton, { backgroundColor: isDark ? theme.colors.card : '#000', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }]}
                onPress={() => handleServicePress('see-more')}
                activeOpacity={0.7}
              >
                <Text style={[styles.seeMoreText, { color: isDark ? theme.colors.text : '#fff', marginRight: 8 }]}>See More</Text>
                <ArrowDownIcon size={20} color={isDark ? theme.colors.text : '#fff'} />
              </TouchableOpacity>
            </GlassyBox>

          </ScrollView>
        </Animated.View>

        <ClipboardPhoneModal
          visible={showClipboardModal}
          phoneNumber={clipboardPhone || ''}
          onIgnore={handleIgnoreClipboard}
          onUse={handleUseClipboard}
        />
        <MoreProductsModal
          visible={showMoreProductsModal}
          onClose={() => setShowMoreProductsModal(false)}
          onSuggest={() => {
            setShowMoreProductsModal(false);
            // Optionally, navigate to a suggestion screen or show a toast
          }}
        />
      </RNSafeAreaView>
    </View>
  );
};

export default CustomHomeScreen;