import React, { useEffect, useRef, useState } from "react"
import { View, StyleSheet, StatusBar, SafeAreaView, Animated, Text, ImageBackground, Image } from "react-native"
import type { NativeStackScreenProps } from "@react-navigation/native-stack"
import type { RootStackParamList } from "../../types/navigation"
import { useTheme } from "../../components/ThemeContext"
import logger from '../../services/productionLogger'
import authSessionService from '../../services/authSessionService'

type Props = NativeStackScreenProps<RootStackParamList, "Splash">

const SplashScreen = ({ navigation }: Props) => {
  const { isDark } = useTheme()

  // Simple, clean animations
  const fadeAnim = useRef(new Animated.Value(0)).current
  const scaleAnim = useRef(new Animated.Value(0.8)).current
  const logoRotateAnim = useRef(new Animated.Value(0)).current

  const [loadingText, setLoadingText] = useState('Welcome to Vendy')

  useEffect(() => {
    // Simple, elegant entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1200,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 40,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start()

    // Subtle logo rotation
    Animated.loop(
      Animated.timing(logoRotateAnim, {
        toValue: 1,
        duration: 8000,
        useNativeDriver: true,
      })
    ).start()

    // Simple loading text progression
    const loadingSteps = [
      'Welcome to Vendy',
      'Initializing...',
      'Almost ready...',
    ]

    let stepIndex = 0
    const stepInterval = setInterval(() => {
      if (stepIndex < loadingSteps.length - 1) {
        stepIndex++
        setLoadingText(loadingSteps[stepIndex])
      }
    }, 1000)

    // Authentication check and navigation
    const checkAuthAndNavigate = async () => {
      try {
        setLoadingText('Checking authentication...')
        const session = await authSessionService.checkExistingSession();

        if (session.isAuthenticated) {
          const setupStatus = session.setupStatus;
          const userInfo = session.user;
          const hasName = userInfo?.firstName && userInfo.firstName.trim() !== '';
          const hasProfileSetup = setupStatus?.hasProfileSetup || hasName;

          if (!setupStatus) {
            navigation.replace('Startup');
          } else if (!hasProfileSetup) {
            navigation.replace('NameSetup', { userData: userInfo });
          } else if (!setupStatus.hasPinSetup) {
            navigation.replace('PinSetup', { userData: userInfo });
          } else {
            navigation.replace('PinVerification', { user: userInfo });
          }
        } else {
          navigation.replace('Startup');
        }
      } catch (error) {
        logger.error('Auth check failed', error, 'splash');
        navigation.replace('Startup');
      }
    }

    const timer = setTimeout(checkAuthAndNavigate, 3500);

    return () => {
      clearInterval(stepInterval);
      clearTimeout(timer);
    }
  }, [navigation])

  const logoRotation = logoRotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  })

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={isDark ? "light-content" : "dark-content"}
        backgroundColor="transparent"
        translucent={true}
      />

      {/* Background Image */}
      <ImageBackground
        source={isDark ? require("../../../assets/images/bg.jpeg") : require("../../../assets/images/bg-white.jpeg")}
        style={StyleSheet.absoluteFillObject}
        resizeMode="cover"
      />

      {/* Main Content Container */}
      <View style={styles.content}>
        {/* Logo Section - Clean and Centered */}
        <Animated.View
          style={[
            styles.logoSection,
            {
              opacity: fadeAnim,
              transform: [
                { scale: scaleAnim },
                { rotate: logoRotation },
              ],
            },
          ]}
        >
          <Image
            source={require("../../../assets/icons/vendy.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </Animated.View>

        {/* Brand Text Section */}
        <Animated.View
          style={[
            styles.textSection,
            {
              opacity: fadeAnim,
            },
          ]}
        >
          <Text style={styles.brandText}>VENDY</Text>
          <Text style={styles.tagline}>Fast • Secure • Reliable</Text>
        </Animated.View>

        {/* Loading Text */}
        <Animated.View
          style={[
            styles.loadingSection,
            {
              opacity: fadeAnim,
            },
          ]}
        >
          <Text style={styles.loadingText}>{loadingText}</Text>
        </Animated.View>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  logoSection: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 60,
  },
  logo: {
    width: 120,
    height: 120,
  },
  textSection: {
    alignItems: 'center',
    marginBottom: 80,
  },
  brandText: {
    fontSize: 36,
    fontWeight: '700',
    color: '#FFFFFF',
    letterSpacing: 4,
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  tagline: {
    fontSize: 14,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    letterSpacing: 1,
  },
  loadingSection: {
    alignItems: 'center',
    position: 'absolute',
    bottom: 100,
    width: '100%',
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    letterSpacing: 0.5,
  },
})

export default SplashScreen
