export type RootStackParamList = {
  Splash: undefined;
  Startup: undefined;
  EmailInput: undefined;
  EmailVerification: { email: string };
  AvatarSelection: { userData?: any };
  NameSetup: { userData?: any };
  PinSetup: { userData?: any };
  PinReset: { resetToken: string; userData?: any };
  PinVerification: { user?: any };
  OtpVerification: { email?: string; userData?: any };
  BiometricSetup: { userData?: any };
  SetupComplete: { userData?: any };
  SetupLoading: { message?: string; next?: keyof RootStackParamList; userData?: any } | undefined;

  MainTabs: undefined;
  Home: undefined;
  Profile: { userId: string };
  Settings: undefined;
  Security: undefined;
  Appearance: undefined;
  ReferEarn: undefined;
  Airtime: undefined;
};

export type BottomTabParamList = {
  HomeTab: undefined;
  ServicesTab: undefined;
  WalletTab: undefined;
  HistoryTab: undefined;
  RewardsTab: undefined;
  PaymentsTab: undefined;
  ProfileTab: undefined;
};
