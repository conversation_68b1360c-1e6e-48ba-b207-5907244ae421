{"name": "payvendy", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.6", "@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/auth": "^22.2.1", "@react-native-firebase/crashlytics": "^22.2.1", "@react-native-firebase/messaging": "^22.2.1", "@react-native-firebase/perf": "^22.2.1", "@react-native-google-signin/google-signin": "^14.0.2", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@react-navigation/stack": "^7.3.3", "@types/crypto-js": "^4.2.2", "crypto-js": "^4.2.0", "immer": "^10.1.1", "jwt-decode": "^4.0.0", "react": "^19.0.0", "react-native": "^0.78.0", "react-native-biometrics": "^3.0.1", "react-native-contacts": "^7.0.4", "react-native-device-info": "^14.0.4", "react-native-gesture-handler": "^2.26.0", "react-native-haptic-feedback": "^2.3.3", "react-native-keychain": "^10.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^3.2.0", "react-native-modal": "^14.0.0-rc.1", "react-native-permissions": "^5.4.1", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-select-contact": "^1.6.3", "react-native-ssl-pinning": "^1.6.0", "react-native-svg": "^15.12.0", "react-native-touch-id": "^4.4.1", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.0", "@react-native/eslint-config": "0.78.0", "@react-native/metro-config": "0.78.0", "@react-native/typescript-config": "0.78.0", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "metro": "^0.82.4", "metro-react-native-babel-preset": "^0.77.0", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}