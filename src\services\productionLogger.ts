import { ENV_CONFIG, DEBUG_CONFIG } from '../config/environment';

/**
 * Production-safe logger that replaces console.log statements
 * Automatically disables logging in production builds for performance
 */

interface LogLevel {
  DEBUG: 0;
  INFO: 1;
  WARN: 2;
  ERROR: 3;
}

interface LogEntry {
  level: keyof LogLevel;
  message: string;
  data?: any;
  timestamp: number;
  source?: string;
}

class ProductionLogger {
  private isProduction = !__DEV__;
  private logLevel: keyof LogLevel = DEBUG_CONFIG.LOG_LEVEL as keyof LogLevel || 'error';
  private logBuffer: LogEntry[] = [];
  private maxBufferSize = 100;

  // Lazy import to avoid circular dependency
  private getCrashReporting() {
    try {
      return require('./crashReportingService').default;
    } catch (error) {
      return null;
    }
  }
  
  private levels: LogLevel = {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3,
  };

  private shouldLog(level: keyof LogLevel): boolean {
    return this.levels[level] >= this.levels[this.logLevel];
  }

  private addToBuffer(entry: LogEntry): void {
    this.logBuffer.push(entry);
    
    // Keep buffer size manageable
    if (this.logBuffer.length > this.maxBufferSize) {
      this.logBuffer = this.logBuffer.slice(-this.maxBufferSize);
    }
  }

  private formatMessage(level: keyof LogLevel, message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level}]`;
    
    if (data) {
      return `${prefix} ${message} ${JSON.stringify(data)}`;
    }
    return `${prefix} ${message}`;
  }

  /**
   * Debug logging - only in development
   */
  debug(message: string, data?: any, source?: string): void {
    if (!this.shouldLog('DEBUG')) return;
    
    const entry: LogEntry = {
      level: 'DEBUG',
      message,
      data,
      timestamp: Date.now(),
      source,
    };
    
    this.addToBuffer(entry);
    
    if (__DEV__) {
      console.log(this.formatMessage('DEBUG', message, data));
    }
  }

  /**
   * Info logging - development and staging
   */
  info(message: string, data?: any, source?: string): void {
    if (!this.shouldLog('INFO')) return;
    
    const entry: LogEntry = {
      level: 'INFO',
      message,
      data,
      timestamp: Date.now(),
      source,
    };
    
    this.addToBuffer(entry);
    
    if (__DEV__ || DEBUG_CONFIG.ENABLE_API_LOGGING) {
      console.log(this.formatMessage('INFO', message, data));
    }
  }

  /**
   * Warning logging - all environments but throttled in production
   */
  warn(message: string, data?: any, source?: string): void {
    if (!this.shouldLog('WARN')) return;
    
    const entry: LogEntry = {
      level: 'WARN',
      message,
      data,
      timestamp: Date.now(),
      source,
    };
    
    this.addToBuffer(entry);
    
    if (__DEV__) {
      console.warn(this.formatMessage('WARN', message, data));
    } else {
      // In production, send warnings to crash reporting
      try {
        const crashReporting = this.getCrashReporting();
        if (crashReporting) {
          crashReporting.addBreadcrumb({
            category: 'warning',
            message,
            level: 'warning',
            data: data || {},
          });
        }
      } catch (error) {
        // Fallback to silent logging
      }
    }
  }

  /**
   * Error logging - all environments, always sent to crash reporting
   */
  error(message: string, error?: Error | any, source?: string): void {
    let errorData = error;
    // If error is not an Error instance, try to serialize it for better logging
    if (!(error instanceof Error)) {
      if (typeof error === 'object' && error !== null) {
        try {
          errorData = JSON.stringify(error);
        } catch (e) {
          errorData = String(error);
        }
      } else {
        errorData = String(error);
      }
    }
    const entry: LogEntry = {
      level: 'ERROR',
      message,
      data: errorData,
      timestamp: Date.now(),
      source,
    };
    this.addToBuffer(entry);
    if (__DEV__) {
      console.error(this.formatMessage('ERROR', message, errorData));
    }
    // Always send errors to crash reporting in all environments
    try {
      const crashReporting = this.getCrashReporting();
      if (crashReporting) {
        if (error instanceof Error) {
          crashReporting.recordError(error, {
            message,
            source,
            timestamp: entry.timestamp,
          });
        } else {
          crashReporting.addBreadcrumb({
            category: 'error',
            message,
            level: 'error',
            data: { error: errorData, source },
          });
        }
      }
    } catch (crashError) {
      if (__DEV__) {
        console.error('Failed to log to crash reporting:', crashError);
      }
    }
  }

  /**
   * Performance logging for API calls and operations
   */
  performance(operation: string, duration: number, data?: any): void {
    if (!DEBUG_CONFIG.ENABLE_PERFORMANCE_MONITORING) return;
    
    const message = `${operation} completed in ${duration}ms`;
    this.info(message, data, 'performance');
    
    // Log slow operations as warnings
    if (duration > 2000) { // 2+ seconds
      this.warn(`Slow operation detected: ${operation}`, { duration, ...data });
    }
  }

  /**
   * API logging with automatic performance tracking
   */
  api(method: string, url: string, status: number, duration: number, data?: any): void {
    const message = `${method} ${url} - ${status} (${duration}ms)`;
    
    if (status >= 400) {
      this.error(message, data, 'api');
    } else if (duration > 1000) {
      this.warn(message, data, 'api');
    } else if (DEBUG_CONFIG.ENABLE_API_LOGGING) {
      this.info(message, data, 'api');
    }
  }

  /**
   * Security event logging
   */
  security(event: string, data?: any): void {
    const message = `Security event: ${event}`;
    this.warn(message, data, 'security');
    
    // Always record security events
    try {
      const crashReporting = this.getCrashReporting();
      if (crashReporting) {
        crashReporting.addBreadcrumb({
          category: 'security',
          message,
          level: 'warning',
          data: data || {},
        });
      }
    } catch (error) {
      // Silent fallback
    }
  }

  /**
   * User action logging (for analytics)
   */
  userAction(action: string, data?: any): void {
    if (!DEBUG_CONFIG.ENABLE_PERFORMANCE_MONITORING) return;
    
    this.debug(`User action: ${action}`, data, 'user');
  }

  /**
   * Get recent log buffer (for debugging)
   */
  getRecentLogs(count: number = 50): LogEntry[] {
    return this.logBuffer.slice(-count);
  }

  /**
   * Clear log buffer
   */
  clearBuffer(): void {
    this.logBuffer = [];
  }

  /**
   * Get log statistics
   */
  getStats(): {
    bufferSize: number;
    logLevel: string;
    isProduction: boolean;
    recentErrors: number;
    recentWarnings: number;
  } {
    const recent = this.logBuffer.slice(-50);
    const recentErrors = recent.filter(entry => entry.level === 'ERROR').length;
    const recentWarnings = recent.filter(entry => entry.level === 'WARN').length;
    
    return {
      bufferSize: this.logBuffer.length,
      logLevel: this.logLevel,
      isProduction: this.isProduction,
      recentErrors,
      recentWarnings,
    };
  }

  /**
   * Legacy console replacement methods
   */
  log = this.info.bind(this);
}

// Create singleton instance
export const logger = new ProductionLogger();

// Legacy exports for backward compatibility
export const console_log = logger.log.bind(logger);
export const console_warn = logger.warn.bind(logger);
export const console_error = logger.error.bind(logger);

// Default export
export default logger;
