-- =====================================================
-- FIX AUTH PROVIDER COLUMN MIGRATION
-- =====================================================
-- This migration ensures the auth_provider column exists
-- Run this in your database to fix the Google authentication issue

-- Add auth_provider column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'auth_provider'
    ) THEN
        ALTER TABLE users ADD COLUMN auth_provider VARCHAR(50) DEFAULT 'phone';
        RAISE NOTICE 'Added auth_provider column to users table';
    ELSE
        RAISE NOTICE 'auth_provider column already exists';
    END IF;
END $$;

-- Add constraint for auth_provider values if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.constraint_column_usage 
        WHERE table_name = 'users' 
        AND constraint_name = 'check_auth_provider'
    ) THEN
        ALTER TABLE users ADD CONSTRAINT check_auth_provider 
        CHECK (auth_provider IN ('phone', 'google', 'email'));
        RAISE NOTICE 'Added check_auth_provider constraint';
    ELSE
        RAISE NOTICE 'check_auth_provider constraint already exists';
    END IF;
END $$;

-- Add google_uid column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'google_uid'
    ) THEN
        ALTER TABLE users ADD COLUMN google_uid VARCHAR(255) UNIQUE;
        RAISE NOTICE 'Added google_uid column to users table';
    ELSE
        RAISE NOTICE 'google_uid column already exists';
    END IF;
END $$;

-- Add provider_id column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'provider_id'
    ) THEN
        ALTER TABLE users ADD COLUMN provider_id VARCHAR(255);
        RAISE NOTICE 'Added provider_id column to users table';
    ELSE
        RAISE NOTICE 'provider_id column already exists';
    END IF;
END $$;

-- Add picture column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'picture'
    ) THEN
        ALTER TABLE users ADD COLUMN picture TEXT;
        RAISE NOTICE 'Added picture column to users table';
    ELSE
        RAISE NOTICE 'picture column already exists';
    END IF;
END $$;

-- Update existing users to have auth_provider = 'phone' if NULL
UPDATE users SET auth_provider = 'phone' WHERE auth_provider IS NULL;

-- Create indexes for new columns if they don't exist
CREATE INDEX IF NOT EXISTS idx_users_google_uid ON users(google_uid);
CREATE INDEX IF NOT EXISTS idx_users_auth_provider ON users(auth_provider);

-- Update the phone_number constraint to allow NULL for Google users
ALTER TABLE users ALTER COLUMN phone_number DROP NOT NULL;

-- Create a partial unique index that only applies to non-null phone numbers
DROP INDEX IF EXISTS users_phone_number_key;
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_phone_number_unique 
ON users(phone_number) WHERE phone_number IS NOT NULL;

-- Migration completed successfully
SELECT 'Auth provider columns added/verified successfully' AS migration_status;
