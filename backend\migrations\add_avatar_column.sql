-- Add avatar column to users table
-- This allows storing local avatar paths like "assets/male/1.png"

-- Add avatar column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'avatar'
    ) THEN
        ALTER TABLE users ADD COLUMN avatar TEXT;
        COMMENT ON COLUMN users.avatar IS 'Avatar image path or URL for user profile picture';
    END IF;
END $$;

-- Update existing users who have picture but no avatar
UPDATE users 
SET avatar = picture 
WHERE picture IS NOT NULL 
AND (avatar IS NULL OR avatar = '');

-- Create index for faster avatar queries
CREATE INDEX IF NOT EXISTS idx_users_avatar ON users(avatar) WHERE avatar IS NOT NULL;

-- Migration completed successfully
-- Avatar column added to users table
