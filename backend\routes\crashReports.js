const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const authService = require('../services/authService');
const logger = require('../utils/logger');
const { getSupabase } = require('../config/database');

const router = express.Router();

// Rate limiting for crash reports
const crashReportLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // limit each IP to 10 crash reports per minute
  message: {
    error: 'Too many crash reports, please try again later.',
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * @route   POST /api/v1/crash-reports
 * @desc    Submit crash report
 * @access  Private
 */
router.post('/',
  authService.protect,
  crashReportLimiter,
  [
    body('errorId')
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage('Error ID is required and must be a valid string'),
    body('message')
      .isString()
      .isLength({ min: 1, max: 1000 })
      .withMessage('Error message is required and must be less than 1000 characters'),
    body('timestamp')
      .isISO8601()
      .withMessage('Valid timestamp is required'),
    body('appVersion')
      .isString()
      .isLength({ min: 1, max: 20 })
      .withMessage('App version is required'),
    body('platform')
      .isIn(['ios', 'android'])
      .withMessage('Platform must be ios or android'),
    body('deviceInfo')
      .isObject()
      .withMessage('Device info must be an object'),
    body('breadcrumbs')
      .isArray()
      .withMessage('Breadcrumbs must be an array')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const {
        errorId,
        message,
        stack,
        componentStack,
        timestamp,
        appVersion,
        platform,
        deviceInfo,
        breadcrumbs
      } = req.body;

      const userId = req.user.id;

      logger.info(`Crash report received from user ${userId}`, {
        errorId,
        message: message.substring(0, 100) + '...',
        platform,
        appVersion
      });

      // Store crash report in database
      const supabase = getSupabase();
      const { data, error } = await supabase
        .from('crash_reports')
        .insert([{
          error_id: errorId,
          user_id: userId,
          message: message,
          stack: stack,
          component_stack: componentStack,
          timestamp: timestamp,
          app_version: appVersion,
          platform: platform,
          device_info: deviceInfo,
          breadcrumbs: breadcrumbs,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        logger.error('Failed to store crash report:', error);
        return res.status(500).json({
          status: 'error',
          message: 'Failed to store crash report'
        });
      }

      // Log critical errors for immediate attention
      if (message.toLowerCase().includes('fatal') || 
          message.toLowerCase().includes('crash') ||
          stack?.includes('ReferenceError') ||
          stack?.includes('TypeError')) {
        logger.error('CRITICAL CRASH REPORT', {
          errorId,
          userId,
          message,
          platform,
          appVersion,
          deviceModel: deviceInfo?.model
        });
      }

      res.status(200).json({
        status: 'success',
        message: 'Crash report received successfully',
        data: {
          reportId: data.id,
          errorId: errorId
        }
      });

    } catch (error) {
      logger.error('Crash report submission error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   GET /api/v1/crash-reports
 * @desc    Get crash reports for admin
 * @access  Private (Admin only)
 */
router.get('/',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const { page = 1, limit = 50, platform, severity } = req.query;
      const offset = (page - 1) * limit;

      const supabase = getSupabase();
      let query = supabase
        .from('crash_reports')
        .select(`
          id,
          error_id,
          user_id,
          message,
          timestamp,
          app_version,
          platform,
          device_info,
          created_at,
          users!inner(email, first_name, last_name)
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (platform) {
        query = query.eq('platform', platform);
      }

      const { data: reports, error } = await query;

      if (error) {
        logger.error('Failed to fetch crash reports:', error);
        return res.status(500).json({
          status: 'error',
          message: 'Failed to fetch crash reports'
        });
      }

      // Get total count for pagination
      let countQuery = supabase
        .from('crash_reports')
        .select('id', { count: 'exact', head: true });

      if (platform) {
        countQuery = countQuery.eq('platform', platform);
      }

      const { count, error: countError } = await countQuery;

      if (countError) {
        logger.warn('Failed to get crash reports count:', countError);
      }

      res.status(200).json({
        status: 'success',
        data: {
          reports: reports || [],
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count || 0,
            pages: Math.ceil((count || 0) / limit)
          }
        }
      });

    } catch (error) {
      logger.error('Get crash reports error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   GET /api/v1/crash-reports/:id
 * @desc    Get specific crash report details
 * @access  Private (Admin only)
 */
router.get('/:id',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const { id } = req.params;

      const supabase = getSupabase();
      const { data: report, error } = await supabase
        .from('crash_reports')
        .select(`
          *,
          users!inner(email, first_name, last_name, phone_number)
        `)
        .eq('id', id)
        .single();

      if (error || !report) {
        return res.status(404).json({
          status: 'error',
          message: 'Crash report not found'
        });
      }

      res.status(200).json({
        status: 'success',
        data: report
      });

    } catch (error) {
      logger.error('Get crash report details error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   DELETE /api/v1/crash-reports/:id
 * @desc    Delete crash report
 * @access  Private (Admin only)
 */
router.delete('/:id',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const { id } = req.params;

      const supabase = getSupabase();
      const { error } = await supabase
        .from('crash_reports')
        .delete()
        .eq('id', id);

      if (error) {
        logger.error('Failed to delete crash report:', error);
        return res.status(500).json({
          status: 'error',
          message: 'Failed to delete crash report'
        });
      }

      logger.info(`Crash report ${id} deleted by admin ${req.user.id}`);

      res.status(200).json({
        status: 'success',
        message: 'Crash report deleted successfully'
      });

    } catch (error) {
      logger.error('Delete crash report error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

module.exports = router;
