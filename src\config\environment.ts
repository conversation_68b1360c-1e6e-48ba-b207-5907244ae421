import { Platform } from 'react-native';

interface EnvironmentConfig {
  API_BASE_URL: string;
  API_TIMEOUT: number;
  MAX_RETRY_ATTEMPTS: number;
  CACHE_TTL: number;
  ENABLE_SSL_PINNING: boolean;
  ENABLE_REQUEST_SIGNING: boolean;
  MAX_PIN_ATTEMPTS: number;
  PIN_LOCKOUT_DURATION: number;
  ENABLE_BIOMETRIC_FALLBACK: boolean;
  SSL_PINNING_DOMAINS: string[];
}

// Environment detection
const isDevelopment = __DEV__;
const isProduction = !__DEV__;

// Helper function to determine the best API URL for development
const getDevApiUrl = (): string => {
  // You can change this flag to switch between localtunnel and localhost
  const USE_LOCALHOST = false; // Set to true if localtunnel is down

  let apiUrl: string;

  if (USE_LOCALHOST) {
    if (Platform.OS === 'android') {
      apiUrl = 'http://*************:8000/api/v1'; // Android emulator localhost
    } else {
      apiUrl = 'http://localhost:8000/api/v1'; // iOS simulator localhost
    }
    console.log('🏠 [CONFIG] Using localhost API:', apiUrl);
  } else {
    // Localtunnel configuration (default)
    // *************
    //My IPv4: http://*************:8000/api/v1
    apiUrl = 'http://*************:8000/api/v1';
    console.log('🌐 [CONFIG] Using localtunnel API:', apiUrl);
  }

  return apiUrl;
};

// Development configuration
const developmentConfig: EnvironmentConfig = {
  API_BASE_URL: getDevApiUrl(),
  API_TIMEOUT: 15000, // 15 seconds for dev
  MAX_RETRY_ATTEMPTS: 3,
  CACHE_TTL: 300000, // 5 minutes
  ENABLE_SSL_PINNING: getDevApiUrl().startsWith('https'), // Only enable for HTTPS
  ENABLE_REQUEST_SIGNING: true,
  MAX_PIN_ATTEMPTS: 5,
  PIN_LOCKOUT_DURATION: 300000, // 5 minutes
  ENABLE_BIOMETRIC_FALLBACK: true,
  SSL_PINNING_DOMAINS: ['funny-poems-slide.loca.lt'], // Localtunnel domain with verified certificate hash
};

// Production configuration
const productionConfig: EnvironmentConfig = {
  API_BASE_URL: 'https://api.vendy.com/api/v1', // Production API domain
  API_TIMEOUT: 10000, // 10 seconds for prod
  MAX_RETRY_ATTEMPTS: 2, // Fewer retries for faster failure
  CACHE_TTL: 600000, // 10 minutes
  ENABLE_SSL_PINNING: true, // Always enabled in production
  ENABLE_REQUEST_SIGNING: true,
  MAX_PIN_ATTEMPTS: 3,
  PIN_LOCKOUT_DURATION: 900000, // 15 minutes
  ENABLE_BIOMETRIC_FALLBACK: false, // Stricter in production
  SSL_PINNING_DOMAINS: ['api.vendy.com', 'supabase.co'], // Production domains to pin
};

// Staging configuration
const stagingConfig: EnvironmentConfig = {
  API_BASE_URL: 'https://staging-api.vendy.com/api/v1', // Staging API domain
  API_TIMEOUT: 12000,
  MAX_RETRY_ATTEMPTS: 2,
  CACHE_TTL: 300000,
  ENABLE_SSL_PINNING: true,
  ENABLE_REQUEST_SIGNING: true,
  MAX_PIN_ATTEMPTS: 3,
  PIN_LOCKOUT_DURATION: 600000, // 10 minutes
  ENABLE_BIOMETRIC_FALLBACK: true,
  SSL_PINNING_DOMAINS: ['staging-api.vendy.com', 'supabase.co'], // Staging domains to pin
};

// Select configuration based on environment
const getEnvironmentConfig = (): EnvironmentConfig => {
  // Check for environment variables first (most secure)
  if (process.env.NODE_ENV === 'production') {
    return productionConfig;
  } else if (process.env.NODE_ENV === 'staging') {
    return stagingConfig;
  } else {
    return developmentConfig;
  }
};

// Export the active configuration
export const ENV_CONFIG = getEnvironmentConfig();

// SSL Certificate Pinning Configuration
export const SSL_PINNING_CONFIG = {
  enabled: ENV_CONFIG.ENABLE_SSL_PINNING,
  domains: ENV_CONFIG.SSL_PINNING_DOMAINS,
  failOnMismatch: true,
  allowBackupPins: true,
  // Certificate hashes should be updated when certificates are rotated
  certificateHashes: {
    'funny-poems-slide.loca.lt': [
      // Actual localtunnel certificate public key hash (obtained 2025-07-03)
      'IjUc/BEPc+Jj43d+KZ+l1tyAuXfBaYeIvLuvQDaa2nY=',
      // Let's Encrypt E5 intermediate CA (issuer for loca.lt)
      'jQJTbIh0grw0/1TkHSumWb+Fs0Ggogr621gT3PvPKG0=',
      // Let's Encrypt root CA backup
      'sRHdihwgkaib1P1gxX8HFszlD+7/gTfNvuAybgLPNis=',
    ],
    'api.vendy.com': [
      // Production-ready certificate hashes (covers all major hosting providers)
      'C5+lpZ7tcVwmwQIMcRtPbsQtWLABXhQzejna0wHFr8M=', // ISRG Root X1 (Let's Encrypt - most common)
      'jQJTbIh0grw0/1TkHSumWb+Fs0Ggogr621gT3PvPKG0=', // Let's Encrypt R3 intermediate
      'i7WTqTvh0OioIruIfFR4kMPnBqrS2rdiVPl/s2uC/CY=', // DigiCert Global Root G2 (premium hosting)
      'gkehGfSbEgVOjDfzMvqt6X7F+MyVWx/1VihuqVvpW5s=', // Cloudflare Universal SSL
      'VjLZe/p3W/PJnd6lL8JVNBCGQBZynFLdZSTIqcO0SJ8=', // Amazon Root CA 1 (AWS)
      'VhQGmi/XwBuHqznoFHFHUGONNXBPkINm4fZfQTvTRHU=', // DigiCert TLS RSA SHA256 2020 CA1
    ],
    'staging-api.vendy.com': [
      // Staging environment certificate hashes (same coverage as production)
      'C5+lpZ7tcVwmwQIMcRtPbsQtWLABXhQzejna0wHFr8M=', // ISRG Root X1 (Let's Encrypt)
      'jQJTbIh0grw0/1TkHSumWb+Fs0Ggogr621gT3PvPKG0=', // Let's Encrypt R3 intermediate
      'i7WTqTvh0OioIruIfFR4kMPnBqrS2rdiVPl/s2uC/CY=', // DigiCert Global Root G2
      'gkehGfSbEgVOjDfzMvqt6X7F+MyVWx/1VihuqVvpW5s=', // Cloudflare Universal SSL
    ],
    'supabase.co': [
      // Supabase uses Cloudflare certificates
      'gkehGfSbEgVOjDfzMvqt6X7F+MyVWx/1VihuqVvpW5s=', // Cloudflare Inc ECC CA-3
      'i7WTqTvh0OioIruIfFR4kMPnBqrS2rdiVPl/s2uC/CY=', // DigiCert Global Root G2
      'VhQGmi/XwBuHqznoFHFHUGONNXBPkINm4fZfQTvTRHU=', // DigiCert TLS RSA SHA256 2020 CA1
    ],
  },
};

// Request signing configuration
export const SIGNING_CONFIG = {
  algorithm: 'HS256',
  clockTolerance: 30, // 30 seconds
  issuer: 'vendy-mobile-app',
  audience: 'vendy-api',
};

// Performance optimization settings
export const PERFORMANCE_CONFIG = {
  MAX_CONCURRENT_REQUESTS: 6, // Optimal for mobile
  REQUEST_QUEUE_SIZE: 50,
  CACHE_SIZE_LIMIT: 100, // Number of cached responses
  IMAGE_CACHE_SIZE: 200 * 1024 * 1024, // 200MB for images
  ENABLE_REQUEST_DEDUPLICATION: true,
  ENABLE_PREFETCHING: true,
  PREFETCH_DELAY: 100, // ms
};

// Base security headers (version headers added dynamically by appVersionService)
export const SECURITY_HEADERS = {
  'X-Requested-With': 'VendyMobileApp',
  'X-Platform': Platform.OS,
  'X-Device-Type': Platform.select({
    ios: 'ios',
    android: 'android',
    default: 'unknown',
  }),
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

// Debug configuration
export const DEBUG_CONFIG = {
  ENABLE_API_LOGGING: isDevelopment,
  ENABLE_PERFORMANCE_MONITORING: true,
  ENABLE_CRASH_REPORTING: isProduction,
  LOG_LEVEL: isDevelopment ? 'debug' : 'error',
};

export default ENV_CONFIG;
