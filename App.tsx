import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { StatusBar, LogBox } from 'react-native';
import { ThemeProvider } from "./src/components/ThemeContext"
import ErrorBoundary from './src/components/ErrorBoundary';
import BottomTabNavigator from './src/navigation/BottomTabNavigator';
import ProfileScreen from './src/screens/Profile/ProfileScreen';
import StartupScreen from './src/screens/Onboarding/StartupScreen';
import SplashScreen from './src/screens/Onboarding/SplashScreen';
import EmailInputScreen from './src/screens/Authentication/EmailInputScreen';
import EmailVerificationScreen from './src/screens/Authentication/EmailVerificationScreen';
import AvatarSelectionScreen from './src/screens/AvatarSelection/AvatarSelectionScreen';
import NameSetupScreen from './src/screens/Authentication/NameSetupScreen';
import PinSetupScreen from './src/screens/Authentication/PinSetupScreen';
import PinResetScreen from './src/screens/Authentication/PinResetScreen';
import ModernPinVerificationScreen from './src/screens/Authentication/ModernPinVerificationScreen';
import OtpVerificationScreen from './src/screens/Authentication/OtpVerificationScreen';
import BiometricSetupScreen from './src/screens/Authentication/BiometricSetupScreen';
import SetupCompleteScreen from './src/screens/Onboarding/SetupCompleteScreen';
import SetupLoadingScreen from './src/screens/Onboarding/SetupLoadingScreen';
import AirtimeScreen from './src/screens/Airtime/AirtimeScreen';
import SettingsScreen from './src/screens/Settings/SettingsScreen';
import SecurityScreen from './src/screens/Security/SecurityScreen';
import AppearanceScreen from './src/screens/Appearance/AppearanceScreen';
import ReferEarnScreen from './src/screens/Refer/ReferEarnScreen';
import { ProfileProvider } from './src/context/ProfileContext';
import { initializeNotifications, getFcmToken } from './src/services/notificationService';
// Import centralized Firebase initialization
import { firebaseInitService } from './src/services/firebaseInitService';

import type { RootStackParamList } from './src/types/navigation';
import { appInit } from './src/services/appInitService';
import { performanceService } from './src/services/performanceService';
import logger from './src/services/productionLogger';

const Stack = createNativeStackNavigator<RootStackParamList>();

// Suppress React Native core component deprecation warnings
LogBox.ignoreLogs([
  'ProgressBarAndroid has been extracted from react-native core',
  'Clipboard has been extracted from react-native core',
  'PushNotificationIOS has been extracted from react-native core',
  'This method is deprecated (as well as all React Native Firebase namespaced API)',
]);

const App = (): React.ReactElement => {
  const [, setIsFirebaseInitialized] = useState(false);

  useEffect(() => {
    // Initialize Firebase and notifications
    const initializeServices = async () => {
      try {
        logger.info('[App] Initializing Firebase and notifications', { module: 'App', action: 'initialize_services' });
        
        // Initialize Firebase first using centralized initialization
        const firebaseReady = await firebaseInitService.initialize();
        if (!firebaseReady) {
          logger.warn('[App] Firebase initialization failed or not supported on this platform, continuing without Firebase features', { module: 'App', action: 'firebase_init', error: 'initialization_failed' });
          setIsFirebaseInitialized(false);
          return;
        }
        
        // Initialize notifications after Firebase is ready
        try {
          await initializeNotifications();
          logger.info('[App] Notifications initialized successfully', { module: 'App', action: 'notifications_init' });
        } catch (notificationError) {
          logger.warn('[App] Notification initialization failed, continuing without notifications', { module: 'App', action: 'notifications_init', error: notificationError instanceof Error ? notificationError.message : String(notificationError) });
        }
        
        // Get FCM token for testing (optional)
        try {
          const token = await getFcmToken();
          if (token) {
            logger.info('[App] FCM Token obtained', { module: 'App', action: 'fcm_token', tokenPrefix: token.substring(0, 20) });
            setIsFirebaseInitialized(true);
          } else {
            logger.warn('[App] FCM token is null, continuing without push notifications', { module: 'App', action: 'fcm_token' });
            setIsFirebaseInitialized(false);
          }
        } catch (tokenError) {
          logger.warn('[App] Failed to get FCM token, continuing without push notifications', { module: 'App', action: 'fcm_token', error: tokenError instanceof Error ? tokenError.message : String(tokenError) });
          setIsFirebaseInitialized(false);
        }
        
        logger.info('[App] Service initialization completed', { module: 'App', action: 'services_init_complete', firebaseReady });
      } catch (error) {
        logger.error('[App] Service initialization error', { module: 'App', action: 'services_init', error: error instanceof Error ? error.message : String(error) });
        // Continue without Firebase features
        setIsFirebaseInitialized(false);
      }
    };
    
    initializeServices();

    // Initialize app in the background without blocking UI
    const initializeApp = async () => {
      try {
        // Start app startup performance tracking
        const startupPerfId = performanceService.startTiming('app_startup_complete');
        logger.info('[App] Starting app initialization in background', { module: 'App', action: 'app_init_start' });
        
        // Run initialization in background
        appInit.initialize().then(success => {
          if (success) {
            performanceService.endTiming(startupPerfId);
            logger.info('[App] App initialization completed successfully', { module: 'App', action: 'app_init_complete', success: true });
          } else {
            logger.error('[App] App initialization failed', { module: 'App', action: 'app_init_complete', success: false });
          }
        });
      } catch (error) {
        logger.error('[App] App initialization error', { module: 'App', action: 'app_init', error: error instanceof Error ? error.message : String(error) });
      }
    };

    initializeApp();
  }, []);

  return (
    <ThemeProvider>
      <ProfileProvider>
        <ErrorBoundary>
          <StatusBar backgroundColor="#111" barStyle="light-content" />
          <NavigationContainer>
            <Stack.Navigator initialRouteName="Splash">
              <Stack.Screen
                name="Splash"
                component={SplashScreen}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="Startup"
                component={StartupScreen}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="EmailInput"
                component={EmailInputScreen}
                options={{ headerShown: false }}
              />
                <Stack.Screen
                  name="EmailVerification"
                  component={EmailVerificationScreen}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
              name="AvatarSelection"
              component={AvatarSelectionScreen}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                      name="NameSetup"
                  component={NameSetupScreen}
              options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="PinSetup"
                      component={PinSetupScreen}
                  options={{ headerShown: false }}
            />
                <Stack.Screen
                  name="PinReset"
                  component={PinResetScreen}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="PinVerification"
                  component={ModernPinVerificationScreen}
                      options={{ headerShown: false }}
                    />
                <Stack.Screen
                  name="OtpVerification"
                  component={OtpVerificationScreen}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
              name="BiometricSetup"
              component={BiometricSetupScreen}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                      name="SetupComplete"
                      component={SetupCompleteScreen}
                      options={{ headerShown: false }}
                    />
                    <Stack.Screen
                      name="SetupLoading"
                  component={SetupLoadingScreen}
              options={{ headerShown: false }}
                />
            <Stack.Screen
              name="Airtime"
              component={AirtimeScreen}
              options={{ headerShown: false }}
            />

            <Stack.Screen 
              name="MainTabs" 
                  component={BottomTabNavigator} 
                  options={{ headerShown: false }}
            />
            <Stack.Screen name="Profile" component={ProfileScreen} />
            <Stack.Screen name="Settings" component={SettingsScreen} options={{ headerShown: false }} />
            <Stack.Screen name="Security" component={SecurityScreen} options={{ headerShown: false }} />
            <Stack.Screen name="Appearance" component={AppearanceScreen} options={{ headerShown: false }} />
            <Stack.Screen name="ReferEarn" component={ReferEarnScreen} options={{ headerShown: false }} />
          </Stack.Navigator>
        </NavigationContainer>
      </ErrorBoundary>
      </ProfileProvider>
    </ThemeProvider>
  );
};



export default App;
